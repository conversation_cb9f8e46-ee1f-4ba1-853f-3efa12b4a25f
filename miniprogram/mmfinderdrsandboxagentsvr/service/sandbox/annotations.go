// Package sandbox 沙箱标注服务
package sandbox

import (
	"context"
	"fmt"
	"image"
	"mmfinderdrsandboxagentsvr/middleware/cos"
	"mmfinderdrsandboxagentsvr/middleware/db"
	"mmfinderdrsandboxagentsvr/model/api/base"
	sandbox_api_model "mmfinderdrsandboxagentsvr/model/api/sandbox"
	"os"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
)

func getCurrentPageInfo(
	ctx context.Context, targetID string, imagePath string,
) (*sandbox_api_model.ActionRespData, error) {
	// 获取DOM XML
	domXML, err := GetDomXML(ctx, targetID)
	if err != nil {
		log.WithContext(ctx, log.Field{Key: "error", Value: err}).Error("获取DOM XML失败")
		domXML = "<html></html>"
	}

	// 获取所有元素矩形
	allElementsRects, err := GetAllElementsRects(ctx, targetID)
	if err != nil {
		log.WithContext(ctx, log.Field{Key: "error", Value: err}).Error("获取所有元素矩形失败")
		allElementsRects = ""
	}

	// 根据imagePath获取图片的宽高
	file, err := os.Open(imagePath)
	if err != nil {
		log.WithContext(ctx, log.Field{Key: "error", Value: err}).Error("打开图片文件失败")
		// 如果打开文件失败，使用默认值
		return &sandbox_api_model.ActionRespData{
			URL:              imagePath,
			DomXML:           domXML,
			AllElementsRects: allElementsRects,
			ScreenWidth:      410, // 默认宽度
			ScreenHeight:     776, // 默认高度
		}, nil
	}
	defer file.Close()

	// 解码图片获取尺寸
	img, _, err := image.Decode(file)
	if err != nil {
		log.WithContext(ctx, log.Field{Key: "error", Value: err}).Error("解码图片失败")
		// 如果解码失败，使用默认值
		return &sandbox_api_model.ActionRespData{
			URL:              imagePath,
			DomXML:           domXML,
			AllElementsRects: allElementsRects,
			ScreenWidth:      410, // 默认宽度
			ScreenHeight:     776, // 默认高度
		}, nil
	}

	// 获取图片尺寸
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// 上传图片到COS bucket
	var imgURL string
	imgURL, _, err = cos.UploadImageToBucket(img)
	if err != nil {
		log.WithContext(ctx, log.Field{Key: "error", Value: err}).Error("上传图片至COS bucket失败")
	}

	// 返回结果
	return &sandbox_api_model.ActionRespData{
		URL:              imgURL,
		DomXML:           domXML,
		AllElementsRects: allElementsRects,
		ScreenWidth:      width,  // 使用实际图片宽度
		ScreenHeight:     height, // 使用实际图片高度
	}, nil
}

// findTargetID 查找指定AppID的目标ID
func findTargetID(ctx context.Context, appID string) (string, error) {
	// 获取所有目标列表
	targets, err := GetTargetsAction(ctx)
	if err != nil {
		log.WithContext(ctx, log.Field{Key: "error", Value: err}).Error("获取目标列表失败")
		return "", err
	}

	// 遍历目标列表，查找 URL 中包含指定 appID 的目标
	for _, target := range targets {
		if url, ok := target["url"].(string); ok && strings.Contains(url, appID) {
			if id, ok := target["id"].(string); ok && id != "" {
				return id, nil
			}
		}
	}

	return "", fmt.Errorf("未找到AppID为%s的目标", appID)
}

// closeOpenedApplet 关闭已打开的小程序
func closeOpenedApplet(ctx context.Context, appID string) error {
	// 获取所有目标列表
	targets, err := GetTargetsAction(ctx)
	if err != nil {
		log.WithContext(ctx, log.Field{Key: "error", Value: err}).Error("获取目标列表失败")
		return err
	}

	// 查找并关闭已打开的小程序
	for _, target := range targets {
		if url, ok := target["url"].(string); ok && strings.Contains(url, appID) {
			err = CloseAppletAction(ctx, appID)
			if err != nil {
				return err
			}
			time.Sleep(1000 * time.Millisecond)
		}
	}

	return nil
}

// CreateSandbox 创建沙箱
func CreateSandbox(
	ctx context.Context,
	req *sandbox_api_model.CreateSandboxReq,
) (*base.Resp[sandbox_api_model.CreateSandboxRespData], error) {
	// 如果传了authCode就用，没传的话，就通过userId获取
	authCode := req.AuthCode
	if req.AuthCode == "" {
		data, err := SandboxOnlineObj.GetAuthCode(ctx, req.UserID)
		if err != nil {
			return nil, err
		}
		authCode = data.ILinkAuthCode
	}

	// 登陆微信
	if _, err := SandboxOnlineObj.Login(ctx, authCode); err != nil {
		return nil, err
	}

	// 清除已打开的小程序
	if err := closeOpenedApplet(ctx, req.AppID); err != nil {
		return nil, err
	}

	// 启动小程序页面
	if err := LaunchAppletAction(ctx, req.AppID); err != nil {
		return nil, err
	}

	// 等待小程序加载完成（与 wxms.py 保持一致，等待 5 秒）
	time.Sleep(5 * time.Second)

	// 获取目标ID
	targetID, err := findTargetID(ctx, req.AppID)
	if err != nil {
		return nil, err
	}

	// 启用DOM XML
	if enableErr := EnableDomXML(ctx, targetID); enableErr != nil {
		log.WithContext(ctx, log.Field{Key: "error", Value: enableErr}).Error("启用DOM XML失败")
		return nil, enableErr
	}

	// 获取小程序截图
	imagePath, err := ScreenshotAction(ctx, targetID)
	if err != nil {
		log.WithContext(ctx, log.Field{Key: "error", Value: err}).Error("获取小程序截图失败")
		return nil, err
	}

	// 获取当前页面信息
	actionResp, err := getCurrentPageInfo(ctx, targetID, imagePath)
	if err != nil {
		return nil, err
	}

	return &base.Resp[sandbox_api_model.CreateSandboxRespData]{
		Message: "success",
		Data: &sandbox_api_model.CreateSandboxRespData{
			URL:              actionResp.URL,
			DomXML:           actionResp.DomXML,
			AllElementsRects: actionResp.AllElementsRects,
			ScreenWidth:      actionResp.ScreenWidth,
			ScreenHeight:     actionResp.ScreenHeight,
			TargetID:         targetID,
		},
	}, nil
}

// ExecuteSandboxAction 沙箱交互步骤
func ExecuteSandboxAction(
	ctx context.Context,
	req *sandbox_api_model.ActionReq,
) (*base.Resp[sandbox_api_model.ActionRespData], error) {
	// 根据操作类型执行不同的动作
	if err := executeAction(ctx, req); err != nil {
		return nil, err
	}

	// 获取小程序截图
	imagePath, err := ScreenshotAction(ctx, req.TargetID)
	if err != nil {
		log.WithContext(ctx, log.Field{Key: "error", Value: err}).Error("获取小程序截图失败")
		return nil, err
	}

	// 获取当前页面信息
	actionResp, err := getCurrentPageInfo(ctx, req.TargetID, imagePath)
	if err != nil {
		return nil, err
	}

	// 返回响应
	return &base.Resp[sandbox_api_model.ActionRespData]{
		Message: "success",
		Data:    actionResp,
	}, nil
}

// executeAction 执行具体的沙箱操作
func executeAction(ctx context.Context, req *sandbox_api_model.ActionReq) error {
	switch req.Type {
	case "click":
		return executeClickAction(ctx, req)
	case "scroll":
		return executeScrollAction(ctx, req)
	case "search":
		return executeSearchAction(ctx, req)
	case "wait":
		// 对齐小程序操作脚本
		time.Sleep(3000 * time.Millisecond)
		return nil
	default:
		return fmt.Errorf("不支持的操作类型: %s", req.Type)
	}
}

// executeClickAction 执行点击操作
func executeClickAction(ctx context.Context, req *sandbox_api_model.ActionReq) error {
	if req.MarkConfig == nil || req.Screen == nil {
		return fmt.Errorf("点击操作需要提供markConfig和screen参数")
	}

	// 计算实际点击坐标
	x := (req.MarkConfig.X + req.MarkConfig.Width/2) * float64(req.Screen.ScreenWidth)
	y := (req.MarkConfig.Y + req.MarkConfig.Height/2) * float64(req.Screen.ScreenHeight)

	err := ClickAction(ctx, req.TargetID, x, y)
	if err != nil {
		return err
	}

	// 等待UI元素变化
	time.Sleep(2000 * time.Millisecond)
	return nil
}

// executeScrollAction 执行滚动操作
func executeScrollAction(ctx context.Context, req *sandbox_api_model.ActionReq) error {
	if req.Direction == "" {
		return fmt.Errorf("滚动操作需要提供direction参数")
	}

	// 设置默认坐标和滚动距离
	x := float64(100)
	y := float64(100)
	if req.MarkConfig != nil {
		x = (req.MarkConfig.X + req.MarkConfig.Width/2) * float64(req.Screen.ScreenWidth)
		y = (req.MarkConfig.Y + req.MarkConfig.Height/2) * float64(req.Screen.ScreenHeight)
	} else if req.Screen != nil {
		x = 0.5 * float64(req.Screen.ScreenWidth)
		y = 0.5 * float64(req.Screen.ScreenHeight)
	}

	deltaX := 0.0
	deltaY := float64(req.Length)

	// 根据方向调整滚动方向
	if req.Direction == "down" {
		deltaY = -deltaY
	}

	err := ScrollAction(ctx, req.TargetID, x, y, deltaX, deltaY)
	if err != nil {
		return err
	}

	// 等待UI元素变化
	time.Sleep(2000 * time.Millisecond)
	return nil
}

// executeSearchAction 执行搜索操作
func executeSearchAction(ctx context.Context, req *sandbox_api_model.ActionReq) error {
	if req.Text == "" || req.MarkConfig == nil || req.Screen == nil {
		return fmt.Errorf("搜索操作需要提供text和markConfig和screen参数")
	}

	// 计算实际点击坐标
	x := (req.MarkConfig.X + req.MarkConfig.Width/2) * float64(req.Screen.ScreenWidth)
	y := (req.MarkConfig.Y + req.MarkConfig.Height/2) * float64(req.Screen.ScreenHeight)

	// 点击搜索框
	if err := ClickAction(ctx, req.TargetID, x, y); err != nil {
		return err
	}
	time.Sleep(1000 * time.Millisecond)

	// 输入搜索文本
	if err := InputInsertText(ctx, req.TargetID, req.Text); err != nil {
		return err
	}
	time.Sleep(1000 * time.Millisecond)

	// 按回车键搜索
	if err := PressEnterAction(ctx, req.TargetID); err != nil {
		return err
	}
	time.Sleep(2000 * time.Millisecond)

	return nil
}

// KillSandbox 关闭沙箱
func KillSandbox(ctx context.Context, req *sandbox_api_model.KillSandboxReq) (*base.Resp[any], error) {
	// 退出微信
	if _, err := SandboxOnlineObj.Logout(ctx); err != nil {
		return nil, err
	}

	resp := &base.Resp[any]{
		Message: "success",
	}

	return resp, nil
}

// SubmitAnnotation 提交标注内容
func SubmitAnnotation(req *sandbox_api_model.SubmitAnnotationReq) (*base.Resp[any], error) {
	// 创建一个新的标注记录
	annotation := sandbox_api_model.Annotation{
		RTX:         req.RTX,
		AppID:       req.AppID,
		TargetID:    req.TargetID,
		UserID:      req.UserID,
		Instruction: req.Instruction,
		SubmitTime:  req.SubmitTime,
		Operations:  req.Operations,
		Source:      req.Source,
	}

	err := db.CreateAnnotationWithOperations(&annotation)
	if err != nil {
		resp := &base.Resp[any]{
			Code:    400,
			Message: err.Error(),
		}
		return resp, nil
	}
	// 返回成功
	resp := &base.Resp[any]{
		Code:    0,
		Message: "success",
	}

	return resp, nil
}

// GetAnnotations 获取标注内容列表
func GetAnnotations(
	rtxValues []string, appID string, limit, offset int, isDeleted *int8, isEval *int8, source *int8,
	startTime, endTime *time.Time,
) (*base.Resp[sandbox_api_model.GetAnnotationsRespData], error) {
	// 直接从数据库获取分页数据和总数
	total, pagedAnnotations, err := db.ListPagedAnnotationsWithOperations(
		rtxValues, appID, limit, offset, isDeleted, isEval, source, startTime, endTime,
	)
	if err != nil {
		return nil, err
	}
	// 返回结果
	resp := &base.Resp[sandbox_api_model.GetAnnotationsRespData]{
		Code:    0,
		Message: "success",
		Data: &sandbox_api_model.GetAnnotationsRespData{
			Total:       total,
			Annotations: pagedAnnotations,
		},
	}
	return resp, nil
}

// DeleteAnnotation 软删除标注内容
func DeleteAnnotation(req *sandbox_api_model.DeleteAnnotationReq) (*base.Resp[any], error) {
	err := db.SoftDeleteAnnotation(req.AnnotationID)
	if err != nil {
		return &base.Resp[any]{
			Code:    400,
			Message: err.Error(),
		}, nil
	}
	// 返回成功
	return &base.Resp[any]{
		Code:    0,
		Message: "success",
	}, nil
}

// DeleteAnnotationOperation 软删除标注操作记录
func DeleteAnnotationOperation(req *sandbox_api_model.DeleteAnnotationOperationReq) (*base.Resp[any], error) {
	err := db.SoftDeleteAnnotationOperation(req.AnnotationID, req.OperationID)
	if err != nil {
		return &base.Resp[any]{
			Code:    400,
			Message: err.Error(),
		}, nil
	}
	// 返回成功
	return &base.Resp[any]{
		Code:    0,
		Message: "success",
	}, nil
}

// EvalAnnotation 将标注数据置为评估集
func EvalAnnotation(req *sandbox_api_model.EvalAnnotationReq) (*base.Resp[any], error) {
	// 调用数据库层
	err := db.EvalAnnotation(req.AnnotationID)
	if err != nil {
		return &base.Resp[any]{
			Code:    400,
			Message: err.Error(),
		}, nil
	}

	// 返回成功
	return &base.Resp[any]{
		Code:    0,
		Message: "success",
	}, nil
}
